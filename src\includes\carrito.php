<?php
// Incluir la clase CarritoPersistente
require_once __DIR__ . '/carrito_persistente.php';

// Crear una instancia del carrito persistente
$carritoPersistente = new CarritoPersistente();

// Obtener el total de items y el total del carrito
$totalItems = $carritoPersistente->obtenerTotalItems();
$totalCarrito = $carritoPersistente->obtenerTotalPrecio();

// Mostrar el mini carrito
?>
<div class="carrito-mini">
    <a href="carrito.php" class="carrito-icono">
        <i class="fas fa-shopping-cart"></i>
        <?php if ($totalItems > 0): ?>
            <span class="carrito-contador"><?php echo $totalItems; ?></span>
        <?php endif; ?>
    </a>

    <?php if ($totalItems > 0): ?>
        <div class="carrito-dropdown">
            <div class="carrito-header">
                <h3>Mi Carrito</h3>
                <span class="carrito-total"><?php echo $totalItems; ?> item(s)</span>
            </div>

            <div class="carrito-items">
                <?php foreach ($_SESSION['carrito'] as $item): ?>
                    <div class="carrito-item">
                        <div class="item-info">
                            <div class="item-nombre"><?php echo htmlspecialchars($item['destino']); ?></div>
                            <div class="item-precio">$<?php echo number_format($item['precio'], 2); ?> x <?php echo $item['cantidad']; ?></div>
                        </div>
                        <a href="eliminar_prod.php?id=<?php echo $item['id']; ?>" class="item-eliminar">
                            <i class="fas fa-times"></i>
                        </a>
                    </div>
                <?php endforeach; ?>
            </div>

            <div class="carrito-footer">
                <div class="carrito-total-precio">
                    <span>Total:</span>
                    <span>$<?php echo number_format($totalCarrito, 2); ?></span>
                </div>
                <a href="carrito.php" class="btn-ver-carrito">Ver Carrito</a>
            </div>
        </div>
    <?php endif; ?>
</div>

<style>
    .carrito-mini {
        position: relative;
        margin-left: 20px;
    }

    .carrito-icono {
        position: relative;
        color: #ffffff;
        font-size: 1.2rem;
        text-decoration: none;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .carrito-contador {
        position: absolute;
        top: -8px;
        right: -8px;
        background-color: #e74c3c;
        color: #ffffff;
        font-size: 0.7rem;
        width: 18px;
        height: 18px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .carrito-dropdown {
        position: absolute;
        top: 100%;
        right: 0;
        width: 300px;
        background-color: #ffffff;
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        z-index: 100;
        display: none;
        margin-top: 10px;
    }

    .carrito-mini:hover .carrito-dropdown {
        display: block;
    }

    .carrito-header {
        padding: 15px;
        border-bottom: 1px solid #e0f7fa;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .carrito-header h3 {
        margin: 0;
        color: #023e8a;
        font-size: 1rem;
    }

    .carrito-total {
        color: #666;
        font-size: 0.9rem;
    }

    .carrito-items {
        max-height: 300px;
        overflow-y: auto;
    }

    .carrito-item {
        padding: 15px;
        border-bottom: 1px solid #e0f7fa;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .item-info {
        flex: 1;
    }

    .item-nombre {
        color: #023e8a;
        font-weight: 600;
        margin-bottom: 5px;
    }

    .item-precio {
        color: #666;
        font-size: 0.9rem;
    }

    .item-eliminar {
        color: #e74c3c;
        text-decoration: none;
        font-size: 0.9rem;
        margin-left: 10px;
    }

    .carrito-footer {
        padding: 15px;
        border-top: 1px solid #e0f7fa;
    }

    .carrito-total-precio {
        display: flex;
        justify-content: space-between;
        margin-bottom: 15px;
        font-weight: 600;
        color: #023e8a;
    }

    .btn-ver-carrito {
        display: block;
        background-color: #0077b6;
        color: #ffffff;
        text-align: center;
        padding: 10px;
        border-radius: 4px;
        text-decoration: none;
        transition: background-color 0.3s ease;
    }

    .btn-ver-carrito:hover {
        background-color: #023e8a;
    }
</style>