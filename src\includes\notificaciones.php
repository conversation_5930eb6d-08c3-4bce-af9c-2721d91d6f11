<?php
// Clase para manejar las ofertas turísticas
class OfertaTuristica {
    public $id;
    public $destino;
    public $precio;
    public $descuento;
    public $duracion;
    public $fechaInicio;
    public $fechaFin;
    public $descripcion;
    
    public function __construct($id, $destino, $precio, $descuento, $duracion, $fechaInicio, $fechaFin, $descripcion) {
        $this->id = $id;
        $this->destino = $destino;
        $this->precio = $precio;
        $this->descuento = $descuento;
        $this->duracion = $duracion;
        $this->fechaInicio = $fechaInicio;
        $this->fechaFin = $fechaFin;
        $this->descripcion = $descripcion;
    }
    
    public function getPrecioConDescuento() {
        return $this->precio - ($this->precio * $this->descuento / 100);
    }
}

// Clase para el sistema de notificaciones
class SistemaNotificaciones {
    private $ofertas = [];
    
    public function agregarOferta(OfertaTuristica $oferta) {
        $this->ofertas[] = $oferta;
    }
    
    public function getOfertasMayorDescuento($limite = 3) {
        $ofertas = $this->ofertas;
        usort($ofertas, function($a, $b) {
            return $b->descuento - $a->descuento;
        });
        
        return array_slice($ofertas, 0, $limite);
    }
    
    public function mostrarNotificacionHtml(OfertaTuristica $oferta) {
        $precioFinal = number_format($oferta->getPrecioConDescuento(), 2, ',', '.');
        $html = <<<HTML
        <div class="notificacion-oferta" id="oferta-{$oferta->id}">
            <div class="notificacion-header">
                <h3>{$oferta->destino}</h3>
                <span class="badge-descuento">-{$oferta->descuento}%</span>
            </div>
            <div class="notificacion-body">
                <div class="oferta-detalles">
                    <p class="oferta-descripcion">{$oferta->descripcion}</p>
                    <p class="oferta-duracion"><i class="fa fa-clock"></i> {$oferta->duracion} días</p>
                    <div class="oferta-precio">
                        <span class="precio-anterior">$ {$oferta->precio}</span>
                        <span class="precio-actual">$ {$precioFinal}</span>
                    </div>
                    <p class="oferta-validez">Válido del {$oferta->fechaInicio} al {$oferta->fechaFin}</p>
                </div>
            </div>
            <div class="notificacion-footer">
                <button onclick="reservarOferta({$oferta->id}, '{$oferta->destino}', {$oferta->precio}, {$oferta->descuento})" class="btn-reservar">Reservar ahora</button>
                <button onclick="cerrarNotificacion({$oferta->id})" class="btn-cerrar">Cerrar</button>
            </div>
        </div>
HTML;
        return $html;
    }
}

// Crear ofertas de ejemplo
$sistema = new SistemaNotificaciones();

// Oferta 1: Cancún
$oferta1 = new OfertaTuristica(
    1,
    'Cancún, México',
    1200,
    25,
    7,
    '2025-05-01',
    '2025-06-30',
    'Disfruta de las playas paradisíacas del Caribe mexicano con todo incluido en nuestro resort de 5 estrellas.',
    'https://via.placeholder.com/150x100.png?text=Cancun'
);
$sistema->agregarOferta($oferta1);

// Oferta 2: Roma
$oferta2 = new OfertaTuristica(
    2,
    'Roma, Italia',
    950,
    15,
    5,
    '2025-05-01',
    '2025-05-15',
    'Descubre la ciudad eterna con nuestro paquete que incluye visitas guiadas a los monumentos más emblemáticos.',
    'https://via.placeholder.com/150x100.png?text=Roma'
);
$sistema->agregarOferta($oferta2);

// Oferta 3: Bali
$oferta3 = new OfertaTuristica(
    3,
    'Bali, Indonesia',
    1500,
    30,
    10,
    '2025-06-01',
    '2025-07-31',
    'Sumérgete en la cultura balinesa mientras disfrutas de playas paradisíacas y templos milenarios.',
    'https://via.placeholder.com/150x100.png?text=Bali'
);
$sistema->agregarOferta($oferta3);

// Función JavaScript para cerrar notificaciones
echo '<script>
function cerrarNotificacion(id) {
    var notificacion = document.getElementById("oferta-" + id);
    if (notificacion) {
        notificacion.style.display = "none";
    }
}
</script>';
?>
