<?php include("src/includes/header.php");?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reporte de Hoteles con más Reservas</title>
</head>
<body>
    <div class="container">
        <h1>Reporte de Hoteles con más de 2 Reservas</h1>
        
        <?php
        // Configuración de la conexión
        $servername = "localhost:3306";
        $username = "root"; 
        $password = "root"; 
        $dbname = "agencia";

        // Crear conexión
        $conn = new mysqli($servername, $username, $password, $dbname);

        // Verificar conexión
        if ($conn->connect_error) {
            die('<div class="no-data">Error de conexión: ' . $conn->connect_error . '</div>');
        }

        // Consulta SQL para obtener hoteles con más de 2 reservas
        $sql = "SELECT 
                    h.id_hotel,
                    h.nombre AS hotel_nombre,
                    h.ubicacion,
                    h.tarifa_noche,
                    COUNT(r.id_reserva) AS total_reservas,
                    GROUP_CONCAT(DISTINCT c.nombre, ' ', c.apellido SEPARATOR ', ') AS clientes
                FROM 
                    HOTEL h
                INNER JOIN 
                    RESERVA r ON h.id_hotel = r.id_hotel
                INNER JOIN
                    CLIENTE c ON r.id_cliente = c.id_cliente
                GROUP BY 
                    h.id_hotel, h.nombre, h.ubicacion, h.tarifa_noche
                HAVING 
                    COUNT(r.id_reserva) > 2
                ORDER BY 
                    total_reservas DESC";

        $result = $conn->query($sql);

        if ($result && $result->num_rows > 0) {
            $total_hoteles = $result->num_rows;
            $total_reservas = 0;
            $hoteles_data = array();
            
            // Preparar datos para el resumen y la tabla
            while($row = $result->fetch_assoc()) {
                $total_reservas += $row['total_reservas'];
                $hoteles_data[] = $row;
            }
            
            echo '<table>
                    <thead>
                        <tr>
                            <th>ID Hotel</th>
                            <th>Nombre</th>
                            <th>Ubicación</th>
                            <th>Tarifa/Noche</th>
                            <th>Reservas</th>
                            <th>Clientes</th>
                        </tr>
                    </thead>
                    <tbody>';
            
            foreach ($hoteles_data as $hotel) {
                echo "<tr>
                        <td>".htmlspecialchars($hotel['id_hotel'])."</td>
                        <td>".htmlspecialchars($hotel['hotel_nombre'])."</td>
                        <td>".htmlspecialchars($hotel['ubicacion'])."</td>
                        <td>$".number_format($hotel['tarifa_noche'], 2)."</td>
                        <td class=\"highlight\">".htmlspecialchars($hotel['total_reservas'])."</td>
                        <td>".htmlspecialchars(substr($hotel['clientes'], 0, 50)).(strlen($hotel['clientes']) > 50 ? '...' : '')."</td>
                      </tr>";
            }
            
            echo '</tbody></table>';
        } else {
            echo '<div class="no-data">No se encontraron hoteles con más de 2 reservas en la base de datos.</div>';
        }

        // Cerrar conexión
        $conn->close();
        ?>
    </div>
</body>
</html>

<style>
    body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        margin: 0;
        padding: 20px;
        background-color: #f5f7fa;
        color: #333;
    }
    .container {
        max-width: 1200px;
        margin: 0 auto;
        background: white;
        padding: 30px;
        border-radius: 8px;
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
    }
    h1 {
        color: #2c3e50;
        text-align: center;
        margin-bottom: 30px;
        padding-bottom: 15px;
        border-bottom: 2px solid #3498db;
    }
    .summary-card {
        background: #e8f4fc;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 30px;
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
    }
    .summary-item {
        margin: 10px;
        padding: 15px;
        background: white;
        border-radius: 6px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        flex: 1;
        min-width: 200px;
        text-align: center;
    }
    table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 20px;
    }
    th {
        background-color: #3498db;
        color: white;
        padding: 15px;
        text-align: left;
        position: sticky;
        top: 0;
    }
    td {
        padding: 12px 15px;
        border-bottom: 1px solid #e0e0e0;
    }
    tr:nth-child(even) {
        background-color: #f9f9f9;
    }
    tr:hover {
        background-color: #f1f9ff;
    }
    .no-data {
        text-align: center;
        padding: 30px;
        background: #fff8f8;
        border: 1px dashed #ff6b6b;
        border-radius: 8px;
        color: #e74c3c;
        font-size: 18px;
        margin-top: 20px;
    }
    .highlight {
        font-weight: bold;
        color: #2980b9;
    }
</style>