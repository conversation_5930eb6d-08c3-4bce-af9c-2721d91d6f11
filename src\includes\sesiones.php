<?php
/**
 * Control de sesiones para la aplicación de turismo
 *
 * Este archivo maneja la inicialización, validación y cierre de sesiones
 */

// Configurar parámetros de sesión antes de iniciarla
// MEDIDA 1: Aumentar el tiempo de vida de la cookie de sesión
ini_set('session.cookie_lifetime', 7200); // 2 horas en segundos
// MEDIDA 2: Aumentar el tiempo máximo de vida de la sesión en el servidor
ini_set('session.gc_maxlifetime', 86400); // 24 horas en segundos
// Configurar cookies seguras
ini_set('session.cookie_httponly', 1); // Prevenir acceso a la cookie desde JavaScript
ini_set('session.use_only_cookies', 1); // Forzar el uso de cookies para las sesiones
if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
    ini_set('session.cookie_secure', 1); // Cookies solo por HTTPS si estamos en conexión segura
}

// Iniciar la sesión si no está iniciada
if (session_status() == PHP_SESSION_NONE) {
    session_start();

    // Regenerar ID de sesión periódicamente para prevenir ataques de fijación de sesión
    if (!isset($_SESSION['ultima_regeneracion']) || (time() - $_SESSION['ultima_regeneracion']) > 300) {
        session_regenerate_id(true);
        $_SESSION['ultima_regeneracion'] = time();
    }

    // Generar token CSRF si no existe
    if (empty($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }

    // Actualizar tiempo de último acceso
    $_SESSION['ultimo_acceso'] = time();
    $inactividad = 1800; // 30 minutos

    // Verificar si la sesión ha expirado por inactividad
    if (isset($_SESSION['ultimo_acceso']) && (time() - $_SESSION['ultimo_acceso'] > $inactividad)) {
        session_unset();
        session_destroy();
        header("Location: login.php?msg=sesion_expirada");
        exit();
    }
}

// Verificar si la clase ya está definida para evitar redeclaraciones
if (!class_exists('GestorSesiones')) {

/**
 * Clase para gestionar las sesiones de usuario
 */
class GestorSesiones {

    /**
     * Inicia sesión con las credenciales proporcionadas
     *
     * @param string $usuario Nombre de usuario
     * @param string $password Contraseña del usuario
     * @return bool True si el inicio de sesión fue exitoso, false en caso contrario
     */
    public function iniciarSesion($usuario, $password) {
        // En un entorno real, aquí verificarías las credenciales contra una base de datos
        // Para este ejemplo, usaremos usuarios hardcodeados
        $usuarios = [
            'admin' => [
                'password' => 'admin123',
                'nombre' => 'Administrador',
                'rol' => 'admin'
            ],
            'usuario' => [
                'password' => 'usuario123',
                'nombre' => 'Usuario Regular',
                'rol' => 'usuario'
            ]
        ];

        // Verificar si el usuario existe y la contraseña es correcta
        if (isset($usuarios[$usuario]) && $usuarios[$usuario]['password'] === $password) {
            // Regenerar ID de sesión al iniciar sesión para prevenir ataques de fijación de sesión
            session_regenerate_id(true);

            // Guardar información del usuario en la sesión
            $_SESSION['usuario_id'] = $usuario;
            $_SESSION['usuario_nombre'] = $usuarios[$usuario]['nombre'];
            $_SESSION['usuario_rol'] = $usuarios[$usuario]['rol'];
            $_SESSION['tiempo_inicio'] = time();
            $_SESSION['ultimo_acceso'] = time();
            $_SESSION['ultima_regeneracion'] = time();

            // Establecer una marca de "recordar sesión" si se desea
            $_SESSION['recordar_sesion'] = true;

            // Inicializar contador de actividad para seguimiento
            $_SESSION['contador_actividad'] = 0;

            return true;
        }

        return false;
    }

    /**
     * Verifica si hay una sesión activa
     *
     * @return bool True si hay una sesión activa, false en caso contrario
     */
    public function estaLogueado() {
        return isset($_SESSION['usuario_id']);
    }

    /**
     * Obtiene el nombre del usuario actual
     *
     * @return string|null Nombre del usuario o null si no hay sesión
     */
    public function getNombreUsuario() {
        return $this->estaLogueado() ? $_SESSION['usuario_nombre'] : null;
    }

    /**
     * Obtiene el rol del usuario actual
     *
     * @return string|null Rol del usuario o null si no hay sesión
     */
    public function getRolUsuario() {
        return $this->estaLogueado() ? $_SESSION['usuario_rol'] : null;
    }

    /**
     * Verifica si el usuario tiene un rol específico
     *
     * @param string $rol Rol a verificar
     * @return bool True si el usuario tiene el rol, false en caso contrario
     */
    public function tieneRol($rol) {
        return $this->estaLogueado() && $_SESSION['usuario_rol'] === $rol;
    }

    /**
     * Verifica si la sesión ha expirado
     *
     * @param int $tiempoMaximo Tiempo máximo de inactividad en segundos (por defecto 30 minutos)
     * @return bool True si la sesión ha expirado, false en caso contrario
     */
    public function sesionExpirada($tiempoMaximo = 1800) {
        if (!$this->estaLogueado()) {
            return true;
        }

        $tiempoInactivo = time() - $_SESSION['tiempo_inicio'];

        if ($tiempoInactivo > $tiempoMaximo) {
            $this->cerrarSesion();
            return true;
        }

        // MEDIDA 3: Actualizar todos los tiempos de sesión en cada interacción
        $this->actualizarSesion();

        return false;
    }

    /**
     * Actualiza los tiempos de la sesión para evitar expiración prematura
     * Esta función se llama en cada verificación de sesión para mantenerla activa
     */
    private function actualizarSesion() {
        // Actualizar el tiempo de inicio de sesión
        $_SESSION['tiempo_inicio'] = time();

        // Actualizar el tiempo de último acceso
        $_SESSION['ultimo_acceso'] = time();

        // Regenerar ID de sesión periódicamente (cada 15 minutos)
        if (!isset($_SESSION['ultima_regeneracion']) || (time() - $_SESSION['ultima_regeneracion']) > 900) {
            session_regenerate_id(false); // false para no eliminar la sesión anterior inmediatamente
            $_SESSION['ultima_regeneracion'] = time();
        }

        // Extender la cookie de sesión
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(
                session_name(),
                session_id(),
                time() + 86400, // 24 horas
                $params["path"],
                $params["domain"],
                $params["secure"],
                $params["httponly"]
            );
        }
    }

    /**
     * Cierra la sesión actual
     */
    public function cerrarSesion() {
        $_SESSION = array();

        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params["path"], $params["domain"],
                $params["secure"], $params["httponly"]
            );
        }
        session_destroy();
    }


    public function requiereLogin($loginUrl = 'login.php') {
        if (!$this->estaLogueado() || $this->sesionExpirada()) {
            header("Location: $loginUrl");
            exit;
        }
    }


    public function redirigeSiLogueado($homeUrl = 'index.php') {
        if ($this->estaLogueado() && !$this->sesionExpirada()) {
            header("Location: $homeUrl");
            exit;
        }
    }
}

}
$gestorSesiones = new GestorSesiones();
?>
