<?php
include("src/includes/sesiones.php");
include("src/includes/header.php");
include("PaquetesFiltro.php");
include("src/includes/notificaciones.php");
?>

<body>
    <div class="container">
        <h1>Bienvenido a Vacaciones Tun Tun <PERSON></h1>
        <p>Descubre nuestros mejores paquetes de viaje.</p>
    </div>

    <!-- Sección de Ofertas Destacadas -->
    <div class="ofertas-destacadas-container">
        <h2>Ofertas Especiales</h2>
        <div class="ofertas-slider">
            <?php
            if (isset($sistema) && method_exists($sistema, 'getOfertasMayorDescuento')) {
                $ofertasDestacadas = $sistema->getOfertasMayorDescuento(3);

                if (!empty($ofertasDestacadas)) {
                    foreach ($ofertasDestacadas as $oferta) {
                        echo $sistema->mostrarNotificacionHtml($oferta);
                    }
                } else {
                    echo "<p class='no-ofertas'>No hay ofertas especiales disponibles en este momento.</p>";
                }
            } else {
                echo "<p class='no-ofertas'>Error: No se pudo cargar el sistema de notificaciones.</p>";
                echo "<p>Clases disponibles: " . implode(", ", get_declared_classes()) . "</p>";
            }
            ?>
        </div>
    </div>

    <div class="filtros-container">
        <h2>Filtrar paquetes</h2>
        <form method="GET" action="" class="form-filtros">
            <div class="filtro-grupo">
                <label for="destino">Destino:</label>
                <input type="text" id="destino" name="destino" value="<?php echo isset($_GET['destino']) ? htmlspecialchars($_GET['destino']) : ''; ?>">
            </div>

            <div class="filtro-grupo">
                <label for="hotel">Hotel:</label>
                <input type="text" id="hotel" name="hotel" value="<?php echo isset($_GET['hotel']) ? htmlspecialchars($_GET['hotel']) : ''; ?>">
            </div>

            <div class="filtro-grupo">
                <label for="fecha_inicio">Fecha de ida (desde):</label>
                <input type="text" id="fecha_inicio" name="fecha_inicio" placeholder="DD/MM/YYYY" value="<?php echo isset($_GET['fecha_inicio']) ? htmlspecialchars($_GET['fecha_inicio']) : ''; ?>">
            </div>

            <div class="filtro-grupo">
                <label for="fecha_fin">Fecha de vuelta (hasta):</label>
                <input type="text" id="fecha_fin" name="fecha_fin" placeholder="DD/MM/YYYY" value="<?php echo isset($_GET['fecha_fin']) ? htmlspecialchars($_GET['fecha_fin']) : ''; ?>">
            </div>

            <div class="filtro-grupo">
                <label for="precio_min">Precio mínimo:</label>
                <input type="number" id="precio_min" name="precio_min" value="<?php echo isset($_GET['precio_min']) ? htmlspecialchars($_GET['precio_min']) : ''; ?>">
            </div>

            <div class="filtro-grupo">
                <label for="precio_max">Precio máximo:</label>
                <input type="number" id="precio_max" name="precio_max" value="<?php echo isset($_GET['precio_max']) ? htmlspecialchars($_GET['precio_max']) : ''; ?>">
            </div>

            <div class="filtro-botones">
                <button type="submit" class="btn-filtrar">Filtrar</button>
                <a href="index.php" class="btn-limpiar">Limpiar filtros</a>
            </div>
        </form>
    </div>

    <div class="paquetes-container">
        <h2>Paquetes disponibles</h2>
        <div class="grid-paquetes">
            <?php
            $paquetes = [
                ["id" => 1, "destino" => "Cancún", "precio" => 1200, "vuelo" => "Incluido", "hotel" => "Hotel Cancún Beach", "fecha_ida" => "15/12/2024", "fecha_vuelta" => "22/12/2024"],
                ["id" => 2, "destino" => "Bariloche", "precio" => 1000, "vuelo" => "Incluido", "hotel" => "Hotel Andes", "fecha_ida" => "10/01/2025", "fecha_vuelta" => "20/01/2025"],
                ["id" => 3, "destino" => "Madrid", "precio" => 1800, "vuelo" => "Incluido", "hotel" => "Hotel Centro Madrid", "fecha_ida" => "05/02/2025", "fecha_vuelta" => "15/02/2025"],
                ["id" => 4, "destino" => "París", "precio" => 2000, "vuelo" => "Incluido", "hotel" => "Hotel Eiffel View", "fecha_ida" => "20/03/2025", "fecha_vuelta" => "30/03/2025"],
                ["id" => 5, "destino" => "Tokio", "precio" => 2500, "vuelo" => "Incluido", "hotel" => "Hotel Shibuya", "fecha_ida" => "12/04/2025", "fecha_vuelta" => "26/04/2025"],
                ["id" => 6, "destino" => "Roma", "precio" => 1700, "vuelo" => "Incluido", "hotel" => "Hotel Coliseo", "fecha_ida" => "08/05/2025", "fecha_vuelta" => "18/05/2025"],
                ["id" => 7, "destino" => "Nueva York", "precio" => 1900, "vuelo" => "Incluido", "hotel" => "Hotel Manhattan Central", "fecha_ida" => "15/06/2025", "fecha_vuelta" => "25/06/2025"],
                ["id" => 8, "destino" => "Londres", "precio" => 2100, "vuelo" => "Incluido", "hotel" => "Hotel London Bridge", "fecha_ida" => "10/07/2025", "fecha_vuelta" => "20/07/2025"],
                ["id" => 9, "destino" => "Río de Janeiro", "precio" => 1600, "vuelo" => "Incluido", "hotel" => "Hotel Copacabana", "fecha_ida" => "05/08/2025", "fecha_vuelta" => "15/08/2025"],
            ];

            // Aplicar filtros si hay parámetros GET
            if (!empty($_GET)) {
                $filtro = new PaquetesFiltro($paquetes);
                $paquetesFiltrados = $filtro->aplicarFiltros($_GET);
            } else {
                $paquetesFiltrados = $paquetes;
            }

            if (empty($paquetesFiltrados)) {
                echo "<div class='no-resultados'>No se encontraron paquetes que coincidan con los filtros seleccionados.</div>";
            } else {
                foreach ($paquetesFiltrados as $p) {
                    echo "<div class='paquete'>";
                    echo "<div class='titulo'><h3>" . $p["destino"] . "</h3></div>";
                    echo "<div class='precio'><p>Precio: $" . $p["precio"] . "</p></div>";
                    echo "<div class='boton'><button onclick='toggleDetalles(" . $p["id"] . ")' class='btn-detalles'>Ver detalles</button></div>";
                    echo "<div id='detalles-" . $p["id"] . "' class='detalles-paquete' style='display:none;'>";
                    echo "<p>Vuelo: " . $p["vuelo"] . "</p>";
                    echo "<p>Hotel: " . $p["hotel"] . "</p>";
                    echo "<p>Fecha de ida: " . $p["fecha_ida"] . "</p>";
                    echo "<p>Fecha de vuelta: " . $p["fecha_vuelta"] . "</p>";
                    echo "<div class='detalles-acciones'>";
                    echo "<button onclick='comprarPaquete(" . $p["id"] . ", \"" . $p["destino"] . "\")' class='btn-comprar'>Comprar ahora</button>";

                    // Solo mostrar el botón de agregar al carrito si el usuario está logueado
                    if ($gestorSesiones->estaLogueado()) {
                        echo "<a href='agregar.php?id=" . $p["id"] . "' class='btn-agregar-carrito'><i class='fas fa-cart-plus'></i> Agregar al carrito</a>";
                    } else {
                        echo "<a href='login.php' class='btn-agregar-carrito'><i class='fas fa-sign-in-alt'></i> Inicia sesión para agregar</a>";
                    }

                    echo "</div>";
                    echo "</div>";
                    echo "</div>";
                }
            }
            ?>
        </div>
    </div>

    <script>
        function toggleDetalles(id) {
            const detallesElement = document.getElementById('detalles-' + id);
            if (detallesElement.style.display === 'none') {
                detallesElement.style.display = 'block';
            } else {
                detallesElement.style.display = 'none';
            }
        }

        function comprarPaquete(id, destino) {
            const fechaIda = document.querySelector('#detalles-' + id + ' p:nth-child(3)').textContent.split(': ')[1];
            const fechaVuelta = document.querySelector('#detalles-' + id + ' p:nth-child(4)').textContent.split(': ')[1];

            alert('¡Gracias por comprar el paquete #' + id + ' a ' + destino + '!\n\n' +
                  'Fechas de viaje: ' + fechaIda + ' - ' + fechaVuelta + '\n\n' +
                  'Pronto te contactaremos con más detalles.');
        }

        function reservarOferta(id, destino, precio, descuento) {
            const precioFinal = precio - (precio * descuento / 100);

            alert('¡Gracias por reservar la oferta especial #' + id + ' a ' + destino + '!\n\n' +
                  'Precio final con descuento: €' + precioFinal.toFixed(2) + ' (' + descuento + '% de descuento)\n\n' +
                  'Pronto te contactaremos con más detalles para confirmar tu reserva.');

            // Cerrar la notificación después de reservar
            cerrarNotificacion(id);
        }
    </script>
</body>

<?php include("src/includes/footer.php"); ?>

<style>
    body {
        font-family: 'Segoe UI', sans-serif;
        margin: 0;
        padding: 20px;
        background: #f0f8ff;
    }

    header, footer {
        background: #0077b6;
        color: #fff;
        padding: 15px;
        text-align: center;
    }

    nav a {
        color: #ffffff;
        margin: 0 10px;
        text-decoration: none;
    }

    .container {
        text-align: center;
        background-color: #ffffff;
        padding: 30px;
        border-radius: 12px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.16);
        margin-bottom: 30px;
    }

    /* Estilos para el formulario de filtros */
    .filtros-container {
        max-width: 1200px;
        margin: 0 auto 30px;
        background-color: #ffffff;
        padding: 20px;
        border-radius: 12px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.16);
    }

    .filtros-container h2 {
        text-align: center;
        margin-bottom: 20px;
        color: #023e8a;
    }

    .form-filtros {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
    }

    .filtro-grupo {
        display: flex;
        flex-direction: column;
    }

    .filtro-grupo label {
        margin-bottom: 5px;
        color: #023e8a;
        font-weight: 500;
    }

    .filtro-grupo input {
        padding: 8px;
        border: 1px solid #caf0f8;
        border-radius: 5px;
        font-size: 14px;
    }

    .filtro-botones {
        grid-column: 1 / -1;
        display: flex;
        justify-content: center;
        gap: 15px;
        margin-top: 10px;
    }

    .btn-filtrar {
        padding: 10px 20px;
        background-color: #0077b6;
        color: #ffffff;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        font-size: 14px;
        transition: background-color 0.3s ease;
    }

    .btn-filtrar:hover {
        background-color: #023e8a;
    }

    .btn-limpiar {
        padding: 10px 20px;
        background-color: #90e0ef;
        color: #03045e;
        border: none;
        border-radius: 8px;
        text-decoration: none;
        font-size: 14px;
        transition: background-color 0.3s ease;
    }

    .btn-limpiar:hover {
        background-color: #caf0f8;
    }

    .no-resultados {
        grid-column: 1 / -1;
        text-align: center;
        padding: 20px;
        background-color: #e0f7fa;
        border-radius: 10px;
        color: #023e8a;
        font-size: 16px;
    }

    /* Estilos para las ofertas destacadas */
    .ofertas-destacadas-container {
        max-width: 1200px;
        margin: 0 auto 30px;
        background-color: #ffffff;
        padding: 20px;
        border-radius: 12px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.16);
    }

    .ofertas-destacadas-container h2 {
        text-align: center;
        margin-bottom: 20px;
        color: #023e8a;
    }

    .ofertas-slider {
        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    .no-ofertas {
        text-align: center;
        padding: 20px;
        background-color: #e0f7fa;
        border-radius: 10px;
        color: #023e8a;
        font-size: 16px;
        margin: 10px 0;
    }

    /* Sobrescribir algunos estilos de las notificaciones para que coincidan con nuestro diseño */
    .notificacion-oferta {
        border-radius: 10px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.16);
        background-color: #e0f7fa;
        border: none;
    }

    .notificacion-header {
        background-color: #caf0f8;
        border-bottom: none;
    }

    .notificacion-header h3 {
        color: #03045e;
    }

    .notificacion-footer {
        background-color: #caf0f8;
        border-top: none;
    }

    .btn-reservar {
        display: inline-block;
        margin: 10px;
        padding: 8px 16px;
        background-color: #03045e;
        color: #ffffff;
        border: none;
        border-radius: 8px;
        text-decoration: none;
        transition: background-color 0.3s ease;
        cursor: pointer;
        font-size: 14px;
    }

    .btn-reservar:hover {
        background-color: #023e8a;
    }

    /* Estilos para los paquetes */
    .paquetes-container {
        max-width: 1200px;
        margin: 0 auto;
    }

    .paquetes-container h2 {
        text-align: center;
        margin-bottom: 20px;
        color: #023e8a;
    }

    .grid-paquetes {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        padding: 0 10px;
    }

    .paquete {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background-color: #e0f7fa;
        border-radius: 10px;
        padding: 15px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.16);
    }

    .titulo {
        background-color: #caf0f8;
        color: #03045e;
        border-radius: 10px;
        padding: 10px;
        width: 100%;
        text-align: center;
    }

    .precio {
        color: #023e8a;
        margin: 10px 0;
        font-size: 18px;
    }

    .boton button.btn-detalles {
        display: inline-block;
        padding: 8px 16px;
        background-color: #0077b6;
        color: #ffffff;
        border: none;
        border-radius: 8px;
        text-decoration: none;
        transition: background-color 0.3s ease;
        cursor: pointer;
        font-size: 14px;
    }

    .boton button.btn-detalles:hover {
        background-color: #023e8a;
    }

    .detalles-paquete {
        margin-top: 15px;
        padding: 10px;
        background-color: #caf0f8;
        border-radius: 8px;
        width: 100%;
        text-align: left;
        transition: all 0.3s ease;
    }

    .detalles-paquete p {
        margin: 8px 0;
        color: #023e8a;
    }

    .detalles-acciones {
        display: flex;
        gap: 10px;
        margin-top: 15px;
    }

    .btn-comprar {
        flex: 1;
        padding: 8px 16px;
        background-color: #03045e;
        color: #ffffff;
        border: none;
        border-radius: 8px;
        text-decoration: none;
        transition: background-color 0.3s ease;
        cursor: pointer;
        font-size: 14px;
        text-align: center;
    }

    .btn-comprar:hover {
        background-color: #0077b6;
    }

    .btn-agregar-carrito {
        flex: 1;
        padding: 8px 16px;
        background-color: #0077b6;
        color: #ffffff;
        border: none;
        border-radius: 8px;
        text-decoration: none;
        transition: background-color 0.3s ease;
        cursor: pointer;
        font-size: 14px;
        text-align: center;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 5px;
    }

    .btn-agregar-carrito:hover {
        background-color: #03045e;
    }

    .btn-cerrar {
        display: inline-block;
        margin: 10px;
        padding: 8px 16px;
        background-color:rgb(161, 161, 167);
        color: #ffffff;
        border: none;
        border-radius: 8px;
        text-decoration: none;
        transition: background-color 0.3s ease;
        cursor: pointer;
        font-size: 14px;

    }

    .btn-cerrar:hover {
        background-color:rgb(219, 0, 0);
    }



    .oferta-detalles {
        display: flex;
        flex-direction: column;
        gap: 10px;
        width: 100%;
        margin-top: 10px;
        padding: 10px;
        text-align: left;
        color: #023e8a;
        font-size: 14px;
        line-height: 1.5;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;

    }
</style>
