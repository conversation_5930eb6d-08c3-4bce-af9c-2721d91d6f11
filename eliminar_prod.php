<?php
// Iniciar el buffer de salida para evitar problemas con la redirección
ob_start();

// Incluir el archivo de sesiones
include_once("src/includes/sesiones.php");
// Incluir la clase CarritoPersistente
require_once("src/includes/carrito_persistente.php");

// Verificar si el usuario está logueado
$gestorSesiones->requiereLogin();

// Crear una instancia del carrito persistente
$carritoPersistente = new CarritoPersistente();

// Verificar si se recibió el ID del paquete
if (isset($_GET['id'])) {
    $paqueteId = intval($_GET['id']);

    // Eliminar el producto del carrito persistente
    $resultado = $carritoPersistente->eliminarProducto($paqueteId);

    if ($resultado) {
        $_SESSION['mensaje'] = "El paquete ha sido eliminado del carrito.";
        $_SESSION['tipo_mensaje'] = "success";
    } else {
        // Mensaje de error
        $_SESSION['mensaje'] = "No se pudo eliminar el paquete del carrito.";
        $_SESSION['tipo_mensaje'] = "error";
    }
} else {
    // Mensaje de error
    $_SESSION['mensaje'] = "No se especificó un paquete para eliminar del carrito.";
    $_SESSION['tipo_mensaje'] = "error";
}



// Redirigir a la página del carrito
header("Location: carrito.php");
exit();
?>
