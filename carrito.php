<?php
// Iniciar el buffer de salida para evitar problemas con la redirección
ob_start();

// Incluir el archivo de sesiones
include_once("src/includes/sesiones.php");
// Incluir la clase CarritoPersistente
require_once("src/includes/carrito_persistente.php");
include("src/includes/header.php");

// Verificar si el usuario está logueado
$gestorSesiones->requiereLogin();

// Crear una instancia del carrito persistente
$carritoPersistente = new CarritoPersistente();

// Obtener el total del carrito
$total = $carritoPersistente->obtenerTotalPrecio();
?>

<div class="carrito-container">
    <?php if (isset($_SESSION['mensaje'])): ?>
        <div class="mensaje-<?php echo $_SESSION['tipo_mensaje']; ?>">
            <?php
            echo $_SESSION['mensaje'];
            // Limpiar el mensaje después de mostrarlo
            unset($_SESSION['mensaje']);
            unset($_SESSION['tipo_mensaje']);
            ?>
        </div>
    <?php endif; ?>

    <?php
    // Obtener los productos del carrito
    $productos = $carritoPersistente->obtenerProductos();
    if (empty($productos)):
    ?>
        <div class="carrito-vacio">
            <p>Tu carrito está vacío.</p>
            <a href="index.php" class="btn-continuar">Continuar comprando</a>
        </div>
    <?php else: ?>
        <div class="carrito-items">
            <table class="carrito-tabla">
                <thead>
                    <tr>
                        <th>Paquete</th>
                        <th>Destino</th>
                        <th>Fechas</th>
                        <th>Precio</th>
                        <th>Cantidad</th>
                        <th>Subtotal</th>
                        <th>Acciones</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($productos as $item): ?>
                        <tr>
                            <td class="item-info">
                                <div class="item-destino"><?php echo htmlspecialchars($item['destino']); ?></div>
                                <div class="item-hotel"><?php echo htmlspecialchars($item['hotel']); ?></div>
                            </td>
                            <td><?php echo htmlspecialchars($item['destino']); ?></td>
                            <td><?php echo htmlspecialchars($item['fecha_ida']); ?> - <?php echo htmlspecialchars($item['fecha_vuelta']); ?></td>
                            <td>$<?php echo number_format($item['precio'], 2); ?></td>
                            <td><?php echo $item['cantidad']; ?></td>
                            <td>$<?php echo number_format($item['precio'] * $item['cantidad'], 2); ?></td>
                            <td>
                                <a href="eliminar_prod.php?id=<?php echo $item['id']; ?>" class="btn-eliminar">
                                    <i class="fas fa-trash"></i> Eliminar
                                </a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="5" class="total-label">Total</td>
                        <td colspan="2" class="total-valor">$<?php echo number_format($total, 2); ?></td>
                    </tr>
                </tfoot>
            </table>
        </div>

        <div class="carrito-acciones">
            <a href="index.php" class="btn-continuar">Continuar comprando</a>
            <a href="#" onclick="finalizarCompra()" class="btn-checkout">Proceder al pago</a>
        </div>
    <?php endif; ?>
</div>

<script>
    function finalizarCompra() {
        alert('¡Gracias por tu compra! En breve recibirás un correo con los detalles de tu reserva.');
        // Redirigir a una página que vacía el carrito y luego va a la página principal
        window.location.href = 'vaciar_carrito.php';
    }
</script>

<style>


    .carrito-container {
        max-width: 1200px;
        margin: 0 auto 30px;
        background-color: #ffffff;
        padding: 30px;
        border-radius: 12px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.16);
    }

    .mensaje-success, .mensaje-error {
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 8px;
        text-align: center;
    }

    .mensaje-success {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .mensaje-error {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    .carrito-vacio {
        text-align: center;
        padding: 50px 0;
    }

    .carrito-vacio p {
        font-size: 18px;
        color: #666;
        margin-bottom: 20px;
    }

    .carrito-tabla {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 30px;
    }

    .carrito-tabla th, .carrito-tabla td {
        padding: 15px;
        text-align: left;
        border-bottom: 1px solid #e0f7fa;
    }

    .carrito-tabla th {
        background-color: #e0f7fa;
        color: #023e8a;
        font-weight: 600;
    }

    .item-info {
        display: flex;
        flex-direction: column;
    }

    .item-destino {
        font-weight: 600;
        color: #023e8a;
    }

    .item-hotel {
        font-size: 14px;
        color: #666;
    }

    .total-label {
        text-align: right;
        font-weight: 600;
        color: #023e8a;
        font-size: 18px;
    }

    .total-valor {
        font-weight: 600;
        color: #023e8a;
        font-size: 18px;
    }

    .btn-eliminar {
        color: #e74c3c;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 5px;
        transition: color 0.3s ease;
    }

    .btn-eliminar:hover {
        color: #c0392b;
    }

    .carrito-acciones {
        display: flex;
        justify-content: space-between;
        margin-top: 20px;
    }

    .btn-continuar, .btn-checkout {
        padding: 12px 24px;
        border-radius: 8px;
        text-decoration: none;
        font-weight: 600;
        transition: background-color 0.3s ease;
    }

    .btn-continuar {
        background-color: #e0f7fa;
        color: #023e8a;
    }

    .btn-continuar:hover {
        background-color: #caf0f8;
    }

    .btn-checkout {
        background-color: #0077b6;
        color: #ffffff;
    }

    .btn-checkout:hover {
        background-color: #023e8a;
    }
</style>

<?php include("src/includes/footer.php"); ?>
