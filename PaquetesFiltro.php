<?php
class PaquetesFiltro {
    private $paquetes;

    public function __construct($paquetes) {
        $this->paquetes = $paquetes;
    }

    public function filtrarPorDestino($destino) {
        if (empty($destino)) {
            return $this->paquetes;
        }

        return array_filter($this->paquetes, function($paquete) use ($destino) {
            return stripos($paquete['destino'], $destino) !== false;
        });
    }


    public function filtrarPorFechas($fechaInicio, $fechaFin) {
        if (empty($fechaInicio) && empty($fechaFin)) {
            return $this->paquetes;
        }

        return array_filter($this->paquetes, function($paquete) use ($fechaInicio, $fechaFin) {
            // Convertir fechas a objetos DateTime para comparación
            $fechaIdaPaquete = DateTime::createFromFormat('d/m/Y', $paquete['fecha_ida']);
            $fechaVueltaPaquete = DateTime::createFromFormat('d/m/Y', $paquete['fecha_vuelta']);

            $fechaInicioObj = !empty($fechaInicio) ? DateTime::createFromFormat('d/m/Y', $fechaInicio) : null;
            $fechaFinObj = !empty($fechaFin) ? DateTime::createFromFormat('d/m/Y', $fechaFin) : null;

            // Si solo hay fecha de inicio, verificar que la fecha de ida sea posterior
            if ($fechaInicioObj && !$fechaFinObj) {
                return $fechaIdaPaquete >= $fechaInicioObj;
            }

            // Si solo hay fecha de fin, verificar que la fecha de vuelta sea anterior
            if (!$fechaInicioObj && $fechaFinObj) {
                return $fechaVueltaPaquete <= $fechaFinObj;
            }

            // Si hay ambas fechas, verificar que el viaje esté dentro del rango
            return $fechaIdaPaquete >= $fechaInicioObj && $fechaVueltaPaquete <= $fechaFinObj;
        });
    }

    public function filtrarPorHotel($hotel) {
        if (empty($hotel)) {
            return $this->paquetes;
        }

        return array_filter($this->paquetes, function($paquete) use ($hotel) {
            return stripos($paquete['hotel'], $hotel) !== false;
        });
    }


    public function filtrarPorPrecio($precioMin, $precioMax) {
        if (empty($precioMin) && empty($precioMax)) {
            return $this->paquetes;
        }

        $precioMin = empty($precioMin) ? 0 : intval($precioMin);
        $precioMax = empty($precioMax) ? PHP_INT_MAX : intval($precioMax);

        return array_filter($this->paquetes, function($paquete) use ($precioMin, $precioMax) {
            return $paquete['precio'] >= $precioMin && $paquete['precio'] <= $precioMax;
        });
    }


    public function aplicarFiltros($filtros) {
        $paquetesFiltrados = $this->paquetes;
        $filtro = new PaquetesFiltro($paquetesFiltrados);

        // Filtrar por destino
        if (isset($filtros['destino']) && !empty($filtros['destino'])) {
            $paquetesFiltrados = $filtro->filtrarPorDestino($filtros['destino']);
            $filtro = new PaquetesFiltro($paquetesFiltrados);
        }

        // Filtrar por fechas
        if ((isset($filtros['fecha_inicio']) && !empty($filtros['fecha_inicio'])) ||
            (isset($filtros['fecha_fin']) && !empty($filtros['fecha_fin']))) {
            $fechaInicio = $filtros['fecha_inicio'] ?? '';
            $fechaFin = $filtros['fecha_fin'] ?? '';
            $paquetesFiltrados = $filtro->filtrarPorFechas($fechaInicio, $fechaFin);
            $filtro = new PaquetesFiltro($paquetesFiltrados);
        }

        // Filtrar por hotel
        if (isset($filtros['hotel']) && !empty($filtros['hotel'])) {
            $paquetesFiltrados = $filtro->filtrarPorHotel($filtros['hotel']);
            $filtro = new PaquetesFiltro($paquetesFiltrados);
        }

        // Filtrar por precio
        if ((isset($filtros['precio_min']) && !empty($filtros['precio_min'])) ||
            (isset($filtros['precio_max']) && !empty($filtros['precio_max']))) {
            $precioMin = $filtros['precio_min'] ?? '';
            $precioMax = $filtros['precio_max'] ?? '';
            $paquetesFiltrados = $filtro->filtrarPorPrecio($precioMin, $precioMax);
        }

        return $paquetesFiltrados;
    }
}
?>
