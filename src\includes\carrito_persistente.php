<?php
/**
 * Clase para manejar un carrito de compras persistente usando cookies
 * Permite que los productos del carrito se mantengan incluso después de cerrar sesión
 */
class CarritoPersistente {
    private $cookieName = 'carrito_datos';
    private $cookieExpiry = 2592000; // 30 días en segundos
    private $carrito = [];

    /**
     * Constructor: inicializa el carrito desde la cookie o la sesión
     */
    public function __construct() {
        // Primero intentamos cargar desde la cookie
        if (isset($_COOKIE[$this->cookieName])) {
            $this->carrito = json_decode($_COOKIE[$this->cookieName], true) ?: [];
        }
        
        // Si hay datos en la sesión y no en la cookie, usamos los de la sesión
        if (empty($this->carrito) && isset($_SESSION['carrito']) && !empty($_SESSION['carrito'])) {
            $this->carrito = $_SESSION['carrito'];
            $this->guardarEnCookie();
        }
        
        // Sincronizamos con la sesión para mantener compatibilidad
        $_SESSION['carrito'] = $this->carrito;
    }

    /**
     * Guarda el carrito en una cookie
     */
    private function guardarEnCookie() {
        setcookie(
            $this->cookieName,
            json_encode($this->carrito),
            time() + $this->cookieExpiry,
            '/',
            '',
            false,
            true // httponly
        );
    }

    /**
     * Agrega un producto al carrito
     */
    public function agregarProducto($producto) {
        // Verificar si el producto ya está en el carrito
        $encontrado = false;
        foreach ($this->carrito as &$item) {
            if ($item['id'] == $producto['id']) {
                // Si ya está en el carrito, incrementar la cantidad
                $item['cantidad']++;
                $encontrado = true;
                break;
            }
        }

        // Si no está en el carrito, agregarlo
        if (!$encontrado) {
            $producto['cantidad'] = 1;
            $this->carrito[] = $producto;
        }

        // Actualizar la sesión y la cookie
        $_SESSION['carrito'] = $this->carrito;
        $this->guardarEnCookie();
        
        return true;
    }

    /**
     * Elimina un producto del carrito o reduce su cantidad
     */
    public function eliminarProducto($productoId) {
        if (empty($this->carrito)) {
            return false;
        }

        foreach ($this->carrito as $key => $item) {
            if ($item['id'] == $productoId) {
                // Si la cantidad es mayor a 1, decrementar
                if ($item['cantidad'] > 1) {
                    $this->carrito[$key]['cantidad']--;
                } else {
                    // Si la cantidad es 1, eliminar el producto
                    unset($this->carrito[$key]);
                    // Reindexar el array
                    $this->carrito = array_values($this->carrito);
                }
                
                // Actualizar la sesión y la cookie
                $_SESSION['carrito'] = $this->carrito;
                $this->guardarEnCookie();
                
                return true;
            }
        }
        
        return false;
    }

    /**
     * Obtiene todos los productos del carrito
     */
    public function obtenerProductos() {
        return $this->carrito;
    }

    /**
     * Obtiene el número total de items en el carrito
     */
    public function obtenerTotalItems() {
        if (empty($this->carrito)) {
            return 0;
        }

        $total = 0;
        foreach ($this->carrito as $item) {
            $total += $item['cantidad'];
        }

        return $total;
    }

    /**
     * Obtiene el precio total del carrito
     */
    public function obtenerTotalPrecio() {
        if (empty($this->carrito)) {
            return 0;
        }

        $total = 0;
        foreach ($this->carrito as $item) {
            $total += $item['precio'] * $item['cantidad'];
        }

        return $total;
    }

    /**
     * Vacía el carrito
     */
    public function vaciarCarrito() {
        $this->carrito = [];
        $_SESSION['carrito'] = [];
        setcookie($this->cookieName, '', time() - 3600, '/');
    }
}
?>
