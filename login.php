<?php
// Incluir el archivo de sesiones primero para que esté disponible en el header
include_once("src/includes/sesiones.php");
include("src/includes/header.php");

// Redirigir si ya está logueado
$gestorSesiones->redirigeSiLogueado();

$error = '';

// Procesar el formulario de login
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $usuario = $_POST['usuario'] ?? '';
    $password = $_POST['password'] ?? '';

    if (empty($usuario) || empty($password)) {
        $error = 'Por favor, ingrese usuario y contraseña.';
    } else {
        if ($gestorSesiones->iniciarSesion($usuario, $password)) {
            // Redirigir a la página principal
            header('Location: index.php');
            exit;
        } else {
            $error = 'Usuario o contraseña incorrectos.';
        }
    }
}
?>

<body>
    <div class="login-container">
        <div class="login-form">
            <?php if (!empty($error)): ?>
                <div class="error-message"><?php echo $error; ?></div>
            <?php endif; ?>

            <form method="POST" action="">
                <div class="form-group">
                    <label for="usuario">Usuario:</label>
                    <input type="text" id="usuario" name="usuario" required>
                </div>

                <div class="form-group">
                    <label for="password">Contraseña:</label>
                    <input type="password" id="password" name="password" required>
                </div>

                <div class="form-group">
                    <button type="submit" class="btn-login">Iniciar Sesión</button>
                </div>
            </form>
        </div>
    </div>

    <style>
        .login-container {
            max-width: 500px;
            margin: 0 auto 30px;
            background-color: #ffffff;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.16);
        }

        .login-form {
            width: 100%;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #023e8a;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #caf0f8;
            border-radius: 5px;
            font-size: 16px;
        }

        .btn-login {
            width: 100%;
            padding: 12px;
            background-color: #0077b6;
            color: #ffffff;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s ease;
        }

        .btn-login:hover {
            background-color: #023e8a;
        }

        .error-message {
            background-color: #ffebee;
            color: #c62828;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
        }

        .form-info {
            margin-top: 20px;
            padding: 15px;
            background-color: #e0f7fa;
            border-radius: 5px;
        }

        .form-info p {
            margin-top: 0;
            font-weight: bold;
            color: #023e8a;
        }

        .form-info ul {
            margin-bottom: 0;
            padding-left: 20px;
        }
    </style>
</body>

<?php include("src/includes/footer.php"); ?>
