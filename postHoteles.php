<?php
$servername = "localhost:3306"; 
$username = "root"; 
$password = "root"; 
$dbname = "agencia"; 

$conn = new mysqli($servername, $username, $password, $dbname);

if ($conn->connect_error) {
    die("Conexión fallida: " . $conn->connect_error);
}

$nombre = $_POST['nombre'];
$ubicacion = $_POST['ubicacion'];
$habitaciones = $_POST['habitaciones_disponibles'];
$tarifa = $_POST['tarifa_noche'];

$sql = "INSERT INTO hotel (nombre, ubicacion, habitaciones_disponibles, tarifa_noche) 
        VALUES ('$nombre', '$ubicacion', $habitaciones, $tarifa)";

if ($conn->query($sql) === TRUE) {
    echo "Hotel registrado exitosamente";
} else {
    echo "Error: " . $sql . "<br>" . $conn->error;
}

$conn->close();
?>