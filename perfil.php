<?php
// Incluir el archivo de sesiones primero para que esté disponible en el header
include_once("src/includes/sesiones.php");
include("src/includes/header.php");

// Requerir inicio de sesión para acceder a esta página
$gestorSesiones->requiereLogin();

// Obtener información del usuario
$nombreUsuario = $gestorSesiones->getNombreUsuario();
$rolUsuario = $gestorSesiones->getRolUsuario();
?>

<body>
    <div class="perfil-container">
        <div class="perfil-info">
            <div class="perfil-detalles">
                <h2><?php echo htmlspecialchars($nombreUsuario); ?></h2>
                <p class="perfil-rol">Rol: <span><?php echo htmlspecialchars($rolUsuario); ?></span></p>

                <div class="perfil-stats">
                    <div class="stat">
                        <span class="stat-value">0</span>
                        <span class="stat-label">Reservas</span>
                    </div>
                    <div class="stat">
                        <span class="stat-value">0</span>
                        <span class="stat-label">Reseñas</span>
                    </div>
                    <div class="stat">
                        <span class="stat-value">0</span>
                        <span class="stat-label">Puntos</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="perfil-acciones">
            <h3>Acciones rápidas</h3>
            <div class="acciones-grid">
                <a href="index.php" class="accion-btn">
                    <i class="fa fa-home"></i>
                    <span>Inicio</span>
                </a>
                <a href="logout.php" class="accion-btn logout">
                    <i class="fa fa-sign-out"></i>
                    <span>Cerrar Sesión</span>
                </a>
            </div>
        </div>
    </div>

    <style>
        .perfil-container {
            max-width: 800px;
            margin: 0 auto 30px;
            background-color: #ffffff;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.16);
        }

        .perfil-info {
            display: flex;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #e0f7fa;
        }



        .perfil-detalles {
            flex: 1;
        }

        .perfil-detalles h2 {
            margin-top: 0;
            color: #023e8a;
        }

        .perfil-rol {
            color: #666;
            margin-bottom: 20px;
        }

        .perfil-rol span {
            font-weight: bold;
            color: #0077b6;
            text-transform: capitalize;
        }

        .perfil-stats {
            display: flex;
            gap: 20px;
        }

        .stat {
            text-align: center;
            background-color: #e0f7fa;
            padding: 15px;
            border-radius: 8px;
            flex: 1;
        }

        .stat-value {
            display: block;
            font-size: 24px;
            font-weight: bold;
            color: #023e8a;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
        }

        .perfil-acciones {
            margin-bottom: 30px;
        }

        .perfil-acciones h3, .perfil-admin h3 {
            color: #023e8a;
            margin-top: 0;
        }

        .acciones-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
        }

        .accion-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 15px;
            background-color: #e0f7fa;
            border-radius: 8px;
            text-decoration: none;
            color: #023e8a;
            transition: all 0.3s ease;
        }

        .accion-btn:hover {
            background-color: #caf0f8;
            transform: translateY(-3px);
        }

        .accion-btn i {
            font-size: 24px;
            margin-bottom: 8px;
        }

        .accion-btn.logout {
            background-color: #ffebee;
            color: #c62828;
        }

        .accion-btn.logout:hover {
            background-color: #ffcdd2;
        }


    </style>
</body>

<?php include("src/includes/footer.php"); ?>
