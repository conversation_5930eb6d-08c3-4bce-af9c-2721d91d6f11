<?php
$servername = "localhost:3306";
$username = "root"; 
$password = "root"; 
$dbname = "agencia";

$conn = new mysqli($servername, $username, $password, $dbname);

if ($conn->connect_error) {
    die("Conexión fallida: " . $conn->connect_error);
}

$origen = $_POST['origen'];
$destino = $_POST['destino'];
$fecha = $_POST['fecha'];
$plazas = $_POST['plazas_disponibles'];
$precio = $_POST['precio'];

$sql = "INSERT INTO vuelo (origen, destino, fecha, plazas_disponibles, precio) 
        VALUES ('$origen', '$destino', '$fecha', $plazas, $precio)";

if ($conn->query($sql) === TRUE) {
    echo "Vuelo registrado exitosamente";
} else {
    echo "Error: " . $sql . "<br>" . $conn->error;
}

$conn->close();
?>