<?php
// Incluir el archivo de sesiones primero para que esté disponible en el header
include_once("src/includes/sesiones.php");
include("src/includes/header.php");

// Requerir inicio de sesión para acceder a esta página
$gestorSesiones->requiereLogin();

// Verificar si el usuario tiene rol de administrador
if (!$gestorSesiones->tieneRol('admin')) {
    // Redirigir a la página principal si no es administrador
    header('Location: index.php');
    exit;
}

// Obtener información del usuario
$nombreUsuario = $gestorSesiones->getNombreUsuario();
?>

<body>
    <div class="container">
        <h1>Panel de Administración</h1>
        <p>Bienvenido, <?php echo htmlspecialchars($nombreUsuario); ?>. Desde aquí puedes gestionar el sitio.</p>
    </div>

    <div class="admin-container">
        <div class="admin-sidebar">
            <div class="admin-user">
                <div class="admin-avatar">
                    <img src="https://via.placeholder.com/80" alt="Avatar de administrador">
                </div>
                <div class="admin-info">
                    <h3><?php echo htmlspecialchars($nombreUsuario); ?></h3>
                    <p>Administrador</p>
                </div>
            </div>

            <nav class="admin-nav">
                <a href="#dashboard" class="active"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                <a href="#paquetes"><i class="fas fa-suitcase"></i> Paquetes</a>
                <a href="#usuarios"><i class="fas fa-users"></i> Usuarios</a>
                <a href="#reservas"><i class="fas fa-calendar-check"></i> Reservas</a>
                <a href="#ofertas"><i class="fas fa-percentage"></i> Ofertas</a>
                <a href="#configuracion"><i class="fas fa-cog"></i> Configuración</a>
                <a href="logout.php" class="logout"><i class="fas fa-sign-out-alt"></i> Cerrar Sesión</a>
            </nav>
        </div>

        <div class="admin-content">
            <div id="dashboard" class="admin-section">
                <h2>Dashboard</h2>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-users"></i></div>
                        <div class="stat-info">
                            <h3>Usuarios</h3>
                            <p class="stat-number">254</p>
                            <p class="stat-change positive">+12% <i class="fas fa-arrow-up"></i></p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-suitcase"></i></div>
                        <div class="stat-info">
                            <h3>Paquetes</h3>
                            <p class="stat-number">45</p>
                            <p class="stat-change positive">+5% <i class="fas fa-arrow-up"></i></p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-calendar-check"></i></div>
                        <div class="stat-info">
                            <h3>Reservas</h3>
                            <p class="stat-number">128</p>
                            <p class="stat-change negative">-3% <i class="fas fa-arrow-down"></i></p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-dollar-sign"></i></div>
                        <div class="stat-info">
                            <h3>Ingresos</h3>
                            <p class="stat-number">$45,250</p>
                            <p class="stat-change positive">+8% <i class="fas fa-arrow-up"></i></p>
                        </div>
                    </div>
                </div>

                <div class="recent-activity">
                    <h3>Actividad Reciente</h3>
                    <div class="activity-list">
                        <div class="activity-item">
                            <div class="activity-icon"><i class="fas fa-user-plus"></i></div>
                            <div class="activity-details">
                                <p class="activity-text">Nuevo usuario registrado: <strong>María López</strong></p>
                                <p class="activity-time">Hace 2 horas</p>
                            </div>
                        </div>

                        <div class="activity-item">
                            <div class="activity-icon"><i class="fas fa-shopping-cart"></i></div>
                            <div class="activity-details">
                                <p class="activity-text">Nueva reserva: <strong>Paquete a Cancún</strong></p>
                                <p class="activity-time">Hace 5 horas</p>
                            </div>
                        </div>

                        <div class="activity-item">
                            <div class="activity-icon"><i class="fas fa-edit"></i></div>
                            <div class="activity-details">
                                <p class="activity-text">Paquete actualizado: <strong>Roma, Italia</strong></p>
                                <p class="activity-time">Hace 1 día</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .admin-container {
            display: flex;
            max-width: 1200px;
            margin: 0 auto 30px;
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.16);
            overflow: hidden;
        }

        .admin-sidebar {
            width: 250px;
            background-color: #023e8a;
            color: #ffffff;
            padding: 20px 0;
        }

        .admin-user {
            display: flex;
            align-items: center;
            padding: 0 20px 20px;
            border-bottom: 1px solid #0077b6;
            margin-bottom: 20px;
        }

        .admin-avatar {
            margin-right: 15px;
        }

        .admin-avatar img {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: 2px solid #90e0ef;
        }

        .admin-info h3 {
            margin: 0;
            font-size: 16px;
        }

        .admin-info p {
            margin: 5px 0 0;
            font-size: 14px;
            color: #90e0ef;
        }

        .admin-nav {
            display: flex;
            flex-direction: column;
        }

        .admin-nav a {
            padding: 12px 20px;
            color: #ffffff;
            text-decoration: none;
            display: flex;
            align-items: center;
            transition: background-color 0.3s ease;
        }

        .admin-nav a i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }

        .admin-nav a:hover, .admin-nav a.active {
            background-color: #0077b6;
        }

        .admin-nav a.logout {
            margin-top: 20px;
            border-top: 1px solid #0077b6;
            color: #ffcdd2;
        }

        .admin-content {
            flex: 1;
            padding: 30px;
            overflow-y: auto;
        }

        .admin-section h2 {
            margin-top: 0;
            color: #023e8a;
            border-bottom: 2px solid #e0f7fa;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            display: flex;
            align-items: center;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            background-color: #e0f7fa;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: #0077b6;
            font-size: 20px;
        }

        .stat-info {
            flex: 1;
        }

        .stat-info h3 {
            margin: 0;
            font-size: 14px;
            color: #666;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #023e8a;
            margin: 5px 0;
        }

        .stat-change {
            font-size: 12px;
            margin: 0;
        }

        .positive {
            color: #2ecc71;
        }

        .negative {
            color: #e74c3c;
        }

        .recent-activity h3 {
            color: #023e8a;
            margin-top: 0;
        }

        .activity-list {
            background-color: #f8f9fa;
            border-radius: 8px;
            overflow: hidden;
        }

        .activity-item {
            display: flex;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid #e0f7fa;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            background-color: #e0f7fa;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: #0077b6;
        }

        .activity-details {
            flex: 1;
        }

        .activity-text {
            margin: 0;
            color: #333;
        }

        .activity-time {
            margin: 5px 0 0;
            font-size: 12px;
            color: #666;
        }
    </style>
</body>

<?php include("src/includes/footer.php"); ?>
