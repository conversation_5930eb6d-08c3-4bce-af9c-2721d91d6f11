<?php
// Iniciar el buffer de salida para evitar problemas con la redirección
ob_start();

// Incluir el archivo de sesiones
include_once("src/includes/sesiones.php");
// Incluir la clase CarritoPersistente
require_once("src/includes/carrito_persistente.php");

// Verificar si el usuario está logueado
$gestorSesiones->requiereLogin();

// Crear una instancia del carrito persistente
$carritoPersistente = new CarritoPersistente();

// Verificar si se recibió el ID del paquete
if (isset($_GET['id'])) {
    $paqueteId = intval($_GET['id']);

    // Datos de paquetes (mismos que en index.php)
    $paquetes = [
        ["id" => 1, "destino" => "Cancún", "precio" => 1200, "vuelo" => "Incluido", "hotel" => "Hotel Cancún Beach", "fecha_ida" => "15/12/2024", "fecha_vuelta" => "22/12/2024"],
        ["id" => 2, "destino" => "Bariloche", "precio" => 1000, "vuelo" => "Incluido", "hotel" => "Hotel Andes", "fecha_ida" => "10/01/2025", "fecha_vuelta" => "20/01/2025"],
        ["id" => 3, "destino" => "Madrid", "precio" => 1800, "vuelo" => "Incluido", "hotel" => "Hotel Centro Madrid", "fecha_ida" => "05/02/2025", "fecha_vuelta" => "15/02/2025"],
        ["id" => 4, "destino" => "París", "precio" => 2000, "vuelo" => "Incluido", "hotel" => "Hotel Eiffel View", "fecha_ida" => "20/03/2025", "fecha_vuelta" => "30/03/2025"],
        ["id" => 5, "destino" => "Tokio", "precio" => 2500, "vuelo" => "Incluido", "hotel" => "Hotel Shibuya", "fecha_ida" => "12/04/2025", "fecha_vuelta" => "26/04/2025"],
        ["id" => 6, "destino" => "Roma", "precio" => 1700, "vuelo" => "Incluido", "hotel" => "Hotel Coliseo", "fecha_ida" => "08/05/2025", "fecha_vuelta" => "18/05/2025"],
        ["id" => 7, "destino" => "Nueva York", "precio" => 1900, "vuelo" => "Incluido", "hotel" => "Hotel Manhattan Central", "fecha_ida" => "15/06/2025", "fecha_vuelta" => "25/06/2025"],
        ["id" => 8, "destino" => "Londres", "precio" => 2100, "vuelo" => "Incluido", "hotel" => "Hotel London Bridge", "fecha_ida" => "10/07/2025", "fecha_vuelta" => "20/07/2025"],
        ["id" => 9, "destino" => "Río de Janeiro", "precio" => 1600, "vuelo" => "Incluido", "hotel" => "Hotel Copacabana", "fecha_ida" => "05/08/2025", "fecha_vuelta" => "15/08/2025"],
    ];

    // Buscar el paquete por ID
    $paqueteEncontrado = null;
    foreach ($paquetes as $paquete) {
        if ($paquete['id'] == $paqueteId) {
            $paqueteEncontrado = $paquete;
            break;
        }
    }

    // Verificar si el paquete existe
    if ($paqueteEncontrado) {
        // Agregar el producto al carrito persistente
        $carritoPersistente->agregarProducto($paqueteEncontrado);

        // Mensaje de éxito
        $_SESSION['mensaje'] = "El paquete a {$paqueteEncontrado['destino']} ha sido agregado al carrito.";
        $_SESSION['tipo_mensaje'] = "success";
    } else {
        // Mensaje de error
        $_SESSION['mensaje'] = "El paquete con ID $paqueteId no existe.";
        $_SESSION['tipo_mensaje'] = "error";
    }
} else {
    // Mensaje de error
    $_SESSION['mensaje'] = "No se especificó un paquete para agregar al carrito.";
    $_SESSION['tipo_mensaje'] = "error";
}



// Redirigir a la página anterior o a la página principal
$referer = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : 'index.php';
header("Location: $referer");
exit();
?>
