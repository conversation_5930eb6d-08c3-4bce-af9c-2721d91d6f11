<?php
// Incluir el gestor de sesiones si no está incluido
if (!isset($gestorSesiones)) {
    include_once(__DIR__ . '/sesiones.php');
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Tun Tun Tun Shahur</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body>
  <header>
    <div class="header-container">
      <div class="logo">
        <a href="index.php">Tun Tun Tun Shahur</a>
      </div>
      <nav>
        <a href="index.php">Inicio</a>
        <a href="formularios.php">Formularios</a>
        <a href="query.php">Más reservados</a>
      </nav>
      <div class="header-actions">
        <?php if ($gestorSesiones->estaLogueado()): ?>
          <!-- Incluir el componente del carrito -->
          <?php include_once(__DIR__ . '/carrito.php'); ?>

          <div class="dropdown">
            <button class="dropbtn">
              <i class="fas fa-user-circle"></i>
              <?php echo htmlspecialchars($gestorSesiones->getNombreUsuario()); ?>
              <i class="fas fa-caret-down"></i>
            </button>
            <div class="dropdown-content">
              <a href="perfil.php"><i class="fas fa-user"></i> Mi Perfil</a>
              <a href="carrito.php"><i class="fas fa-shopping-cart"></i> Mi Carrito</a>
              <?php if ($gestorSesiones->tieneRol('admin')): ?>
                <a href="admin.php"><i class="fas fa-cog"></i> Administración</a>
              <?php endif; ?>
              <a href="logout.php"><i class="fas fa-sign-out-alt"></i> Cerrar Sesión</a>
            </div>
          </div>
        <?php else: ?>
          <a href="login.php" class="login-btn"><i class="fas fa-sign-in-alt"></i> Iniciar Sesión</a>
        <?php endif; ?>
      </div>
    </div>
  </header>

  <style>
    header {
      background: #0077b6;
      color: #fff;
      padding: 15px 0;
    }

    .header-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 20px;
    }

    .logo a {
      color: #ffffff;
      font-size: 1.5rem;
      font-weight: bold;
      text-decoration: none;
    }

    nav {
      display: flex;
      gap: 20px;
    }

    nav a {
      color: #ffffff;
      text-decoration: none;
      transition: color 0.3s ease;
    }

    nav a:hover {
      color: #caf0f8;
    }



    .login-btn {
      display: inline-block;
      padding: 8px 16px;
      background-color: #023e8a;
      color: #ffffff;
      border-radius: 4px;
      text-decoration: none;
      transition: background-color 0.3s ease;
    }

    .login-btn:hover {
      background-color: #0096c7;
    }

    .dropdown {
      position: relative;
      display: inline-block;
    }

    .dropbtn {
      background-color: #023e8a;
      color: white;
      padding: 8px 16px;
      font-size: 16px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .dropdown-content {
      display: none;
      position: absolute;
      right: 0;
      background-color: #f9f9f9;
      min-width: 200px;
      box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
      z-index: 1;
      border-radius: 4px;
    }

    .dropdown-content a {
      color: #333;
      padding: 12px 16px;
      text-decoration: none;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .dropdown-content a:hover {
      background-color: #e0f7fa;
    }

    .dropdown:hover .dropdown-content {
      display: block;
    }

    .dropdown:hover .dropbtn {
      background-color: #0096c7;
    }
  </style>

  <main>
